import { Button } from "antd";
import React from "react";

import { CopyOutlined } from "@ant-design/icons";
import { MessageContext } from "@cscs-agent/core";

const Copy: React.FC = () => {
  const context = React.useContext(MessageContext);

  const handleCopy = () => {
    const message = context?.message.getTextContent();
    if (!message) return;
    navigator.clipboard.writeText(message);
  };

  return (
    <Button
      type="text"
      size="small"
      icon={
        <span className="presets:text-light">
          <CopyOutlined onClick={handleCopy} />
        </span>
      }
    ></Button>
  );
};

export default Copy;
